/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 *
 */
package com.stpl.tech.kettle.core.service.impl;

import com.stpl.tech.kettle.core.CashCardStatus;
import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.core.data.vo.OrderFetchStrategy;
import com.stpl.tech.kettle.core.data.vo.OrderStatusData;
import com.stpl.tech.kettle.core.exception.CardValidationException;
import com.stpl.tech.kettle.core.notification.NewOrderNotification;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.service.OrderSearchService;
import com.stpl.tech.kettle.core.service.VoucherManagementService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.DeliveryDao;
import com.stpl.tech.kettle.data.dao.OrderManagementDao;
import com.stpl.tech.kettle.data.dao.OrderSearchDao;
import com.stpl.tech.kettle.data.dao.impl.OrderComplaintDetailDao;
import com.stpl.tech.kettle.data.model.CashCardDetail;
import com.stpl.tech.kettle.data.model.CashCardEventsLogData;
import com.stpl.tech.kettle.data.model.DeliveryDetail;
import com.stpl.tech.kettle.data.model.EmployeeMealData;
import com.stpl.tech.kettle.data.model.OrderComplaintDetailData;
import com.stpl.tech.kettle.data.model.OrderEmailNotification;
import com.stpl.tech.kettle.data.model.OrderRefundDetail;
import com.stpl.tech.kettle.data.model.UnitToDeliveryPartnerMappings;
import com.stpl.tech.kettle.domain.model.CashCard;
import com.stpl.tech.kettle.domain.model.CashCardEventStatus;
import com.stpl.tech.kettle.domain.model.CashCardType;
import com.stpl.tech.kettle.domain.model.DayWiseOrderConsumptionRequest;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderDetailEventRequest;
import com.stpl.tech.kettle.domain.model.OrderDetailTrim;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderItemReviewModal;
import com.stpl.tech.kettle.domain.model.OrderNPS;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.domain.model.UnitClosure;
import com.stpl.tech.kettle.domain.model.UnitMetadata;
import com.stpl.tech.kettle.domain.model.UnitToEdcMappingData;
import com.stpl.tech.master.core.CacheReferenceType;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.data.model.UnitToPartnerEdcMapping;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.notification.BCXOrderDetailTemplate;
import com.stpl.tech.master.notification.OrderDetailEmailNotification;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.TemplateRenderingException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class OrderSearchServiceImpl implements OrderSearchService {

	private static final Logger LOG = LoggerFactory.getLogger(OrderSearchServiceImpl.class);

	@Autowired
	private OrderSearchDao dao;
	
	@Autowired
	private DeliveryDao deliveryDao;

	@Autowired
	private MetadataCache cache;

	@Autowired
	private MasterDataCache masterDataCache;
	@Autowired
	private VoucherManagementService voucherManagementService;
	@Autowired
	private EnvironmentProperties props;

	@Autowired
	private OrderComplaintDetailDao orderComplaintDetailDao;

	private static String NEW_LINE = " \\n ";

	private void setDeliveryDetail(OrderInfo orderInfo) {
		if (orderInfo != null) {
			DeliveryDetail deliveryDetails = null;
			List<DeliveryDetail> details = deliveryDao.getDeliveryDetail(orderInfo.getOrder().getGenerateOrderId());
			if (details != null && details.size() > 0) {
				deliveryDetails = details.get(0);
			}
			orderInfo.setDeliveryDetails(DataConverter.convert(orderInfo.getOrder().getUnitId(), deliveryDetails));
			if (deliveryDetails != null) {
				orderInfo.setDeliveryPartner(cache.getDeliveryPartner(deliveryDetails.getDeliveryPartnerId()));
			}
		}
	}

	/*
	 * @Autowired private DeliveryRequestService deliveryService;
	 */

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.OrderManagementService#getOrderDetail(
	 * int)
	 */
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Order getOrderDetail(int orderId) throws DataNotFoundException {
		return dao.getOrderDetail(orderId);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.OrderManagementService#getOrderDetail(
	 * java.lang.String)
	 */
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Order getOrderDetail(String generatedOrderId) throws DataNotFoundException {
		Order order = dao.getOrderDetail(generatedOrderId);
		try {
			OrderRefundDetail orderRefundDetail = dao.getOrderRefundByOrderId(order.getOrderId());
			if (Objects.nonNull(orderRefundDetail)) {
				order.setOrderRefundDetailId(orderRefundDetail.getOrderRefundDetailId());
			}
		}
		catch (Exception e) {
			LOG.error("Error while fetching service charge order refund detail for order id {}", order.getOrderId());
		}
		return order;
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Order getPartnerOrderDetail(String externalOrderId, int channerPartnerId) throws DataNotFoundException {
		return dao.getPartnerOrderDetail(externalOrderId, channerPartnerId);
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public OrderStatus getPartnerOrderStatus(String externalOrderId, int channerPartnerId) throws DataNotFoundException {
		return dao.getPartnerOrderStatus(externalOrderId, channerPartnerId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<OrderInfo> getOrderToPublish(int id, Date businessDate)
			throws DataNotFoundException, TemplateRenderingException {
		return dao.getOrderToPublish(id, businessDate);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.kettle.core.service.OrderManagementService#createOrder(com.
	 * stpl.tech.kettle.domain.model.Order)
	 */
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<CashCard> validateGiftCards(List<CashCard> cashCards) throws CardValidationException {
		Set<String> giftCardNumbers = new HashSet<>();
		for (CashCard cashCard : cashCards) {
			cashCard.setIsValid(true);
			cashCard.setError("");
			CashCardDetail cashCardDetail = dao.findCashCardByCardNumber(cashCard.getCardNumber(), cashCard.getEmpId(),
					cashCard.getUnitId());
			if (cashCardDetail == null) {
				cashCard.setIsValid(false);
				String error = "Card id entered " + cashCard.getCardNumber() + " for " + cashCard.getProductName()
						+ " does not exist.";
				LOG.info(error);
				cashCard.setError("Card not valid!");
				logCashCardEvent(cashCard, CashCardEventStatus.CARD___VALIDATION___FAILED, error);
			} else {
				if (!cashCardDetail.getCardStatus().equals(CashCardStatus.INITIATED.name())) {
					cashCard.setIsValid(false);
					String error = "Card id entered " + cashCard.getCardNumber() + " for " + cashCard.getProductName()
							+ " has been used already.";
					LOG.info(error);
					cashCard.setError("Card not valid!");
					logCashCardEvent(cashCard, CashCardEventStatus.CARD___VALIDATION___FAILED, error);
				}
				if (cashCardDetail.getCashInitialAmount().compareTo(BigDecimal.valueOf(cashCard.getCardValue())) != 0) {
					cashCard.setIsValid(false);
					cashCard.setError("Card not valid!");
					String error = "Card id entered " + cashCard.getCardNumber() + " for " + cashCard.getProductName()
							+ " belong to a card of " + cashCardDetail.getCashInitialAmount() + " value.";
					LOG.info(error);
					logCashCardEvent(cashCard, CashCardEventStatus.CARD___VALIDATION___FAILED, error);
				}
				if (!giftCardNumbers.add(cashCard.getCardNumber())) {
					cashCard.setError("Card number " + cashCard.getCardNumber() + " already added!");
					cashCard.setCardNumber("");
					cashCard.setIsValid(false);
				}
			}
		}
		return cashCards;
	}

	private void logCashCardEvent(CashCard cashCard, CashCardEventStatus event, String eventData) {
		CashCardEventsLogData cashCardEventsLogData = new CashCardEventsLogData();
		cashCardEventsLogData.setCardNumber(cashCard.getCardNumber());
		cashCardEventsLogData.setEvent(event.value());
		cashCardEventsLogData.setEventDetail(eventData);
		cashCardEventsLogData.setEventTime(AppUtils.getCurrentTimestamp());
		cashCardEventsLogData.setEmpId(cashCard.getEmpId());
		if (cashCard.getUnitId() != null) {
			cashCardEventsLogData.setUnitId(cashCard.getUnitId());
		}
		dao.add(cashCardEventsLogData);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.kettle.core.service.OrderManagementService#createOrder(com.
	 * stpl.tech.kettle.domain.model.Order)
	 */
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean validateGiftCardInOrder(Order order) throws CardValidationException {
		Set<String> giftCardNumbers = new HashSet<>();
		for (OrderItem item : order.getOrders()) {
			if (AppUtils.isGiftCard(item.getCode()) && !voucherManagementService.isGyftrCard(item.getCardType())) {
				if (TransactionUtils.isBillBookOrder(order)) {
					throw new CardValidationException("Gift Card cannot be sold from Bill Book");
				}
				if (!(item.getCardType().equalsIgnoreCase(CashCardType.ECARD.name()))) {
					if (item.getItemCode() == null) {
						throw new CardValidationException("Card id " + item.getItemCode() + " for "
								+ item.getProductName() + " not found in system.");
					} else {
						CashCardDetail cashCardDetail = dao.findCashCardByCardNumber(item.getItemCode(),
								order.getEmployeeId(), order.getUnitId());
						if (cashCardDetail == null
								|| !cashCardDetail.getCardStatus().equals(CashCardStatus.INITIATED.name())
								|| cashCardDetail.getCashInitialAmount().compareTo(item.getPrice()) != 0) {
							throw new CardValidationException("Card id " + item.getItemCode() + " for "
									+ item.getProductName() + " is not valid.");
						}
						if (!giftCardNumbers.add(cashCardDetail.getCardNumber())) {
							throw new CardValidationException(
									"Card id " + item.getItemCode() + " has been added multiple times.");
						}
					}
				}
			}
		}

		return giftCardNumbers.isEmpty();
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.OrderManagementService#
	 * getLastOrderDetail(int)
	 */
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public int getLastOrderDetail(int unitId) throws DataNotFoundException {
		return dao.getLastOrderDetail(unitId);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.kettle.core.service.OrderManagementService#getOrderDetails(
	 * int, java.util.Date, java.util.Date)
	 */
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Order> getOrderDetails(int unitId, Date startTime, Date endTime) throws DataNotFoundException {
		return dao.getOrderDetails(unitId, startTime, endTime, null);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.kettle.core.service.OrderManagementService#getOrderDetails(
	 * int, java.util.Date, java.util.Date)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Order> getOrderDetails(int unitId, Date startTime, Date endTime, OrderFetchStrategy strategy) throws DataNotFoundException {
		return dao.getOrderDetails(unitId, startTime, endTime, strategy);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.OrderManagementService#
	 * getSettlementDetails(int, java.util.Date, java.util.Date)
	 */
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Settlement> getSettlementDetails(int unitId, Date startTime, Date endTime) {
		return dao.getSettlementDetails(unitId, startTime, endTime);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.kettle.core.service.OrderManagementService#getEmailEvents()
	 */
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<OrderEmailNotification> getEmailEvents() {
		return dao.getEmailEvents();
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public OrderInfo getOrderReceipt(int orderId, boolean include, String customerName)
			throws DataNotFoundException, TemplateRenderingException {
		OrderInfo orderInfo = dao.getOrderReceipt(orderId, include, customerName);
		setDeliveryDetail(orderInfo);
		return orderInfo;

	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public OrderInfo getOrderReceipt(String orderId) throws DataNotFoundException, TemplateRenderingException {
		OrderInfo orderInfo = dao.getOrderReceipt(orderId, false);
		setDeliveryDetail(orderInfo);
		return orderInfo;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.OrderManagementService#
	 * getOrderDetailsForDay(int)
	 */
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Order> getOrderDetailsForDay(int unitId) throws DataNotFoundException {
		OrderFetchStrategy strategy = new OrderFetchStrategy(true, true, true, true, false, null, -1, true);
		return dao.getOrderDetails(unitId, getLastDayCloseOrderId(unitId), Integer.MAX_VALUE, strategy);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.OrderManagementService#
	 * getOrderDetailsForDay(int)
	 */
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Order> getTrimmedOrderDetailsForDay(int unitId) throws DataNotFoundException {
		OrderFetchStrategy strategy = new OrderFetchStrategy(false, false, false, false, false, null, -1, true);
		return dao.getOrderDetails(unitId, getLastDayCloseOrderId(unitId), Integer.MAX_VALUE, strategy);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.OrderManagementService#
	 * getOrderDetailsForDay(int)
	 */
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Order> getDeliveryOrderDetailsForDay(int unitId) throws DataNotFoundException {
		OrderFetchStrategy strategy = new OrderFetchStrategy(false, false, false, false, true, null, -1, true);
		return dao.getOrderDetails(unitId, getLastDayCloseOrderId(unitId), Integer.MAX_VALUE, strategy);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.OrderManagementService#
	 * getOrderDetailsForDay(int)
	 */
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Order> getOrderDetailsForDay(int unitId, int terminalId) throws DataNotFoundException {
		OrderFetchStrategy strategy = new OrderFetchStrategy(false, false, false, false, true, null, terminalId, true);
		return dao.getOrderDetails(unitId, getLastDayCloseOrderId(unitId), Integer.MAX_VALUE, strategy);
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Order> getOrderDetailsForDay(int unitId, int startOrderId, int endOrderId)
			throws DataNotFoundException {
		OrderFetchStrategy strategy = new OrderFetchStrategy(true, true, true, true, false, null, -1, true);
		return dao.getOrderDetails(unitId, startOrderId, endOrderId, strategy);
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED, isolation = Isolation.READ_UNCOMMITTED)
	public List<Order> getAllOrdersOfTheDays(DayWiseOrderConsumptionRequest dayWiseOrderConsumptionRequest) {
		OrderFetchStrategy strategy = new OrderFetchStrategy(true, true, true, true, false, null, -1, true);
		return dao.getAllOrdersOfTheDays(dayWiseOrderConsumptionRequest, strategy);
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<OrderStatusData> getOrderStatusForDay(int unitId, List<OrderStatus> orderStatus,
			List<UnitCategory> categoryList,List<String> currentOrderStatus,String generatedOrderId) throws DataNotFoundException {
		List<String> categories = categoryList.stream().map(category -> category.name()).collect(Collectors.toList());
		return dao.getOrderStatusForDay(unitId, orderStatus, categories,currentOrderStatus,generatedOrderId);
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public UnitMetadata getUnitMetadata(int unitId) {
		UnitMetadata unitMetadata = new UnitMetadata();
		unitMetadata.setUnitId(unitId);
		unitMetadata.setDirectGiftCardPurchase(Objects.nonNull(masterDataCache.getCacheReferenceMetadata(CacheReferenceType.DIRECT_PURCHASE_OF_GIFT_CARD))?masterDataCache.getCacheReferenceMetadata(CacheReferenceType.DIRECT_PURCHASE_OF_GIFT_CARD):null);
		unitMetadata.setWalletRecommendationDrools(Objects.nonNull(masterDataCache.getCacheReferenceMetadata(CacheReferenceType.WALLET_RECOMMENDATION_DROOLS_STRATEGY))?masterDataCache.getCacheReferenceMetadata(CacheReferenceType.WALLET_RECOMMENDATION_DROOLS_STRATEGY):null);
		unitMetadata.setLastBusinessDate(dao.getLastBusinessDate(unitId));
		for (UnitToDeliveryPartnerMappings mapping : dao.getUnitToDeliveryPartnerMappings(unitId)) {
			unitMetadata.getDeliveryPartners().add(DataConverter.convert(mapping));
		}

		if(masterDataCache.getUnitPartnerEdcMappingMetadata().containsKey(unitId)
				&& masterDataCache.getUnitPartnerEdcMappingMetadata().get(unitId).size()>0
				&& Objects.nonNull(masterDataCache.getPartnerEdcMappingForUnit(unitId))){
			for(UnitToPartnerEdcMapping mapping: masterDataCache.getPartnerEdcMappingForUnit(unitId)){
				UnitToEdcMappingData unitToEdcMappingData = UnitToEdcMappingData.builder().id(mapping.getId()).unitId(mapping.getUnitId()).partnerName(mapping.getPartnerName()).status(mapping.getStatus())
						.merchantId(mapping.getMerchantId()).merchantKey(mapping.getMerchantKey()).terminalId(mapping.getTerminalId())
						.tId(mapping.getTId()).version(mapping.getVersion()).secretKey(mapping.getSecretKey()).build();
				unitMetadata.getEdcPartnerDetails().add(unitToEdcMappingData);
			}
		}
		return unitMetadata;
	}

	@Override
	public NewOrderNotification returnNewOrders(int unitId, String timeStamp, List<OrderInfo> orderInfos) {

		NewOrderNotification newOrders = new NewOrderNotification();

		DateTimeFormatter timeStampFormat = TransactionConstants.DATE_TIME_FORMATTER_WITH_NO_CHARACTERS;
		DateTimeFormatter orderTimeFormat = TransactionConstants.DATE_TIME_FORMATTER;
		String systemTimeStamp = AppUtils.getCurrentTimeISTStringWithNoColons();
		DateTime currentTime = timeStampFormat
				.parseDateTime((timeStamp != null && !timeStamp.trim().isEmpty()) ? timeStamp : systemTimeStamp);

		// setting current time stamp of the system to new order notification
		newOrders.setTimestamp(systemTimeStamp);

		if (timeStamp != null) {
			Optional<List<OrderInfo>> orders = Optional
					.ofNullable(orderInfos.stream().filter(new Predicate<OrderInfo>() {
						@Override
						public boolean test(OrderInfo info) {
							DateTime orderCreationTime = orderTimeFormat
									.parseDateTime((info.getOrder().getBillCreationTime().toString()));
							return orderCreationTime.getMillis() > currentTime.getMillis()
									|| OrderStatus.CANCELLED_REQUESTED.equals(info.getOrder().getStatus());
						}
					}).collect(Collectors.toList()));

			if (orders.isPresent() && orders.get().size() > 0) {
				newOrders.setOrders(orders.get());
			}
		} else {
			// return all orders from cache for assembly in case the time stamp
			// is null
			newOrders.setOrders(orderInfos);
		}
		return newOrders;
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public int getLastDayCloseOrderId(int unitId) {
		return dao.getLastDayCloseOrderId(unitId);
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<EmployeeMealData> getEmployeeMealData(int employeeId) {
		return dao.getEmployeeMealData(employeeId);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.OrderSearchService#getCostEvent(int)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public int getCostEvent(int orderId) {
		return dao.getCostEvent(orderId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Order> getEmployeeMealOrders(int userId) {
		return dao.getEmployeeMealOrders(userId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public boolean publishFinalOrderStatus(List<Integer> orderIdList) {
		return dao.publishFinalOrderStatus(orderIdList);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.kettle.core.service.OrderSearchService#getOrderDetails(int,
	 * int, int, com.stpl.tech.kettle.core.data.vo.OrderFetchStrategy)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Order> getOrderDetails(int unitId, int startOrderId, int endOrderId, OrderFetchStrategy strategy)
			throws DataNotFoundException {
		return dao.getOrderDetails(unitId, startOrderId, endOrderId, strategy);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<OrderDetailTrim> getOrderDetailsTrim(int unitId, int startOrderId, int endOrderId, OrderFetchStrategy strategy)
			throws DataNotFoundException {
		List<Order> order = dao.getOrderDetails(unitId, startOrderId, endOrderId, strategy);
		return DataConverter.convert( order);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.kettle.core.service.OrderSearchService#getLastBusinessDate(
	 * int)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Date getLastBusinessDate(int unitId) {
		return dao.getLastBusinessDate(unitId);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.core.service.OrderSearchService#
	 * getOrderDetailsForDay(java.util.Date)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Order> getOrderDetailsForDay(Date businessDate) throws DataNotFoundException {
		OrderFetchStrategy strategy = new OrderFetchStrategy(true, true, false, false, false, null, -1, false);
		return dao.getOrderDetails(businessDate, strategy);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Order> getOrderDetailsForDay(Date businessDate, List<Integer> unitIds) {
		OrderFetchStrategy strategy = new OrderFetchStrategy(true, true, false, false, false, null, -1, true);
		return dao.getOrderDetails(businessDate, strategy, unitIds);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Order> getOrderDetailsForDay(Date businessDate, int unitId, String source) {
		OrderFetchStrategy strategy = new OrderFetchStrategy(false, true, false, false, false, null, -1, true);
		return dao.getOrderDetails(businessDate, strategy, unitId, source);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public UnitClosure getLastDayClose(int unitId) {
		return dao.getLastDayClose(unitId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public UnitClosure getUnitClosure(int closureId) {
		return dao.getUnitClosure(closureId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<OrderInfo> getOrderDetailsAfterOrderId(int unitId, int lastOrderId, OrderFetchStrategy strategy,
			List<Integer> skipOrders) {
		return dao.getOrderDetails(unitId, lastOrderId, strategy, skipOrders);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Integer> getSettleOrders(Integer startOrderId, int batchSize){
		return dao.getOrderIdsInBatch(startOrderId, batchSize);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Order> getCustomerOrders(int customerId, Date fromDate, int maxSize, List<Integer> filteredOrderIds) {
		return dao.getCustomerOrders(customerId, fromDate, maxSize, filteredOrderIds);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<OrderNPS> getCustomerFeedbacks(int customerId, Date fromDate, int maxSize, List<Integer> filteredIds) {
		return dao.getCustomerFeedbacks(customerId, fromDate, maxSize, filteredIds);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public OrderNPS getCustomerFeedbacks(int orderId) {
		return dao.getCustomerFeedback(orderId);
	}

	public OrderNPS getFeedbackDetail(int surveyResponseId) throws DataNotFoundException{
		return dao.getFeedbackDetail(surveyResponseId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Integer getCustomerId(Integer orderId) {
		return dao.getCustomerId(orderId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Integer getChannelPartnerId(Integer orderId) {
		return dao.getChannelPartnerId(orderId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public String getPartnerOrderDetail(String partnerSourceId, String issueType){
		try {
			Map<String, Object> res = dao.getPartnerOrderDetail(partnerSourceId, issueType);
			if (Objects.nonNull(res)) {
				return getOrderDetailText(res, partnerSourceId, issueType);
			}
		}catch (Exception e){
			LOG.error("Error in fetching OrderDetail : {}",e);
		}
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public Map<String,Object> fetchPartnerOrderDetail(String partnerSourceId,String issueType){
		try {
			Map<String, Object> res = dao.getPartnerOrderDetail(partnerSourceId, issueType);
			return res;
		}catch (Exception e){
			LOG.error("Error in fetching OrderDetail Object : {}",e);
		}
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean publishPartnerOrderDetail(OrderDetailEventRequest req){
		try {
			Map<String, Object> res = dao.getPartnerOrderDetail(req.getPartnerOrderId(), req.getIssueType());
			res.put("COMPLAINT_TIME",req.getComplaintTime());
			res.put("COMPLAINT_ITEMS",req.getComplaintItem());
			res.put("COMPLAINT_TYPE",req.getType());
			res.put("PREVIOUS_ITEMS",req.getPreviousItems());
			res.put("MODIFIED_ITEMS",req.getModifiedItems());
			List<String> toEmails = new ArrayList<>(Arrays.asList("<EMAIL>", "<EMAIL>"));
			try {
				BCXOrderDetailTemplate bcxOrderDetailTemplate = new BCXOrderDetailTemplate(res, req.getPartnerOrderId(),req.getType(), props.getEnvironmentType(), props.getBasePath());
				OrderDetailEmailNotification orderDetailEmailNotification = new OrderDetailEmailNotification(res,req.getPartnerOrderId(),req.getType(), toEmails, props.getEnvironmentType(), bcxOrderDetailTemplate);
				orderDetailEmailNotification.sendEmail();
			}catch (Exception e){
				LOG.error("Error in sending Email : {}",e);
			}
			try{
				if(AppUtils.isProd(props.getEnvironmentType()) && req.isPublishToFamePilot()){
					LOG.info("Calling FamePilot Api");
					publishToFamePilot(res,req.getPartnerOrderId());
				}
			}catch (Exception e){
				LOG.error("Error in publishing Data to FamePilot : {}",e);
			}
			saveOrderComplaintData(res);
			return true;

		}catch (Exception e){
			LOG.error("Error in publishing Data : {}",e);
		}
		return false;
	}

	private boolean publishToFamePilot(Map<String,Object> map,String partnerOrderId) throws IOException {
		String url = props.getFamePilotUrlForFeedbackEvent();
		List<NameValuePair> famePilotObject = getObjectForFamePilot(map,partnerOrderId);
		try{
			WebServiceHelper.postRequestForFamePilot(url,famePilotObject);
			return true;
		}catch (Exception e){
			LOG.error("Error in calling FamePilot API : {}",e);
		}
		return false;
	}

	private String getOrderDetailText(Map<String,Object> res,String partnerSourceId, String issueType){
		StringBuilder sb = new StringBuilder();
		sb.append("Hi "+res.get("UNIT_NAME")+","+NEW_LINE);
		if(res.containsKey("COMPLAINT_TYPE")) {
			sb.append("We have received a " + res.get("COMPLAINT_TYPE") + " from " + res.get("PARTNER_CODE") + " for " + res.get("BRAND_NAME") + NEW_LINE);
		}else {
			sb.append("We have received a "+ "ORS from " + res.get("PARTNER_CODE") + " for " + res.get("BRAND_NAME") + NEW_LINE);
		}
		sb.append("Below are the details"+NEW_LINE);
		sb.append("Issue Type : "+res.get("ISSUE_TYPE")+NEW_LINE);
		sb.append("Order Id : "+res.get("GENERATED_ORDER_ID")+NEW_LINE);
		sb.append("Partner Order Id : "+partnerSourceId+NEW_LINE);
		sb.append("Customer Name : "+res.get("CUSTOMER_NAME")+NEW_LINE);
		sb.append("Order Time : "+res.get("BILLING_SERVER_TIME")+NEW_LINE);
		sb.append("Processing Time : "+res.get("READY_TO_DISPATCH")+ " ("+res.get("PREPERATION_TIME")+")"+NEW_LINE);
		sb.append("Rider Assigned At : "+ res.get("RIDER_ASSIGNED_AT")+ " ("+res.get("RIDER_ASSIGNED_TIME")+")"+NEW_LINE);
		sb.append("Rider Arrived At : "+ res.get("RIDER_ARRIVED_AT")+ " ("+res.get("RIDER_ARRIVED_TIME")+")"+NEW_LINE);
		sb.append("Pickup Time : "+res.get("ORDER_PICKUP_TIME")+" ("+res.get("RIDER_PICKED_TIME")+")"+NEW_LINE);
		sb.append("Delivery Time : "+res.get("ORDER_DELIVERY_TIME")+" ("+res.get("RIDER_DELIVERY_TIME")+")"+NEW_LINE);
		sb.append("Rider Details : "+res.get("RIDER_NAME")+" ("+res.get("RIDER_CONTACT")+")"+NEW_LINE);
		if(res.containsKey("COMPLAINT_TIME")) {
			sb.append("Customer Complaint Time : " + res.get("COMPLAINT_TIME") + NEW_LINE);
		}
		if(res.containsKey("COMPLAINT_ITEMS")) {
			sb.append("Complaint Items : " + res.get("COMPLAINT_ITEMS") + NEW_LINE);
		}
		if(res.containsKey("PREVIOUS_ITEMS")) {
			sb.append("Previous Items : " + res.get("PREVIOUS_ITEMS") + NEW_LINE);
		}
		if(res.containsKey("MODIFIED_ITEMS")) {
			sb.append("Modified Items : " + res.get("MODIFIED_ITEMS") + NEW_LINE);
		}
		sb.append("All Items : "+ res.get("ITEMS")+NEW_LINE);
		sb.append("Order Amount : "+res.get("TOTAL_AMOUNT")+NEW_LINE);
		sb.append("DAM : "+res.get("CAFE_MANAGER")+ " (" + res.get("CAFE_MANAGER_CONTACT")+ ")" +NEW_LINE);
		sb.append("AM : "+res.get("UNIT_MANAGER")+ " (" + res.get("UNIT_MANAGER_CONTACT")+ ")"+ NEW_LINE);
		sb.append("Cafe Manager : "+res.get("UNIT_CAFE_MANAGER")+NEW_LINE);
		sb.append("Please Look Into This asap" + NEW_LINE +
				"Thanks" + NEW_LINE +
				"BCX Management Team"+NEW_LINE);
		return sb.toString();
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<NameValuePair> getObjectForFamePilot(Map<String,Object> map,String partnerSourceId) throws IOException {
		List<NameValuePair> formData = new ArrayList<>();
		try {
				formData.add(new BasicNameValuePair("business_uuid", map.get("BUSINESS_UID").toString()));
				formData.add(new BasicNameValuePair("branch", map.get("BRANCH_ID").toString()));
				formData.add(new BasicNameValuePair("reviewer_name", map.get("CUSTOMER_NAME").toString()));
				formData.add(new BasicNameValuePair("contact_number", "+************"));
				formData.add(new BasicNameValuePair("email", "NA"));
				formData.add(new BasicNameValuePair("review_time", AppUtils.getCurrentDateTime()));
				formData.add(new BasicNameValuePair("rating", "1"));
				formData.add(new BasicNameValuePair("review_text", getOrderDetailText(map,partnerSourceId,"NA")));
				formData.add(new BasicNameValuePair("review_tags", map.get("ITEMS").toString()));
				formData.add(new BasicNameValuePair("order_id", partnerSourceId));
				formData.add(new BasicNameValuePair("service_type","delivery"));
				formData.add(new BasicNameValuePair("order_items",getOrderItemsText(map)));
				return formData;
		}catch (Exception e){
			LOG.info("Error in creating Object For FamePilot: {}",e);
			return null;
		}
	}

	private String getOrderItemsText(Map<String,Object> res){
		List<OrderItemReviewModal> list = new ArrayList<>();
		String allItemsString = (String) res.get("ITEMS");
		String [] allItems = allItemsString.split(",");
		String complaintItemsString = "";
		if(res.containsKey("COMPLAINT_TIME") && Objects.nonNull(res.get("COMPLAINT_TIME"))) {
			complaintItemsString = (String) res.get("COMPLAINT_TIME");
		}
		if(StringUtils.isEmpty(complaintItemsString)){
			for(String s : allItems){
				OrderItemReviewModal o = new OrderItemReviewModal();
				o.setItemName(s.split("X")[0].trim());
				o.setQuantity(Integer.valueOf(s.split("X")[1].trim()));
				o.setRating(1);
				list.add(o);
			}
		}else{
			for(String s: allItems){
				OrderItemReviewModal o = new OrderItemReviewModal();
				if(complaintItemsString.contains(s.split("X")[0].trim())){
					o.setItemName(s.split("X")[0].trim());
					o.setQuantity(Integer.valueOf(s.split("X")[1].trim()));
					o.setRating(1);
					list.add(o);
				}
			}
		}
		if(!CollectionUtils.isEmpty(list)){
			String resultString = "[";
			for(OrderItemReviewModal o : list){
				if(resultString.equals("[")) {
					resultString = resultString + o.toString();
				}else{
					resultString = resultString + ","+o.toString();
				}
			}
			resultString = resultString + "]";
			return resultString;
		}
		return "";

	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	private void saveOrderComplaintData(Map<String,Object> map){
		try {
			OrderComplaintDetailData orderComplaintDetailData = orderComplaintDetailDao.findByOrderSourceId(map.get("ORDER_SOURCE_ID").toString());
			if(Objects.nonNull(orderComplaintDetailData)){
				return;
			}
			orderComplaintDetailData = new OrderComplaintDetailData();
			orderComplaintDetailData.setUnitName(Objects.toString(map.get("UNIT_NAME"),""));
			Objects.toString(map.get("UNIT_ID"),"");
			orderComplaintDetailData.setUnitId(Integer.parseInt(Objects.toString(map.get("UNIT_ID"),"")));
			orderComplaintDetailData.setUnitRegion(Objects.toString(map.get("UNIT_REGION"),""));
			orderComplaintDetailData.setUnitEmail(Objects.toString(map.get("UNIT_EMAIL"),""));
			orderComplaintDetailData.setUnitManager(Objects.toString(map.get("UNIT_MANAGER"),""));
			orderComplaintDetailData.setUnitManagerId(Objects.toString(map.get("UNIT_MANAGER_ID"),""));
			orderComplaintDetailData.setUnitManagerContact(Objects.toString(map.get("UNIT_MANAGER_CONTACT"),""));
			orderComplaintDetailData.setUnitCafeManager(Objects.toString(map.get("UNIT_CAFE_MANAGER"),""));
			orderComplaintDetailData.setIssueTime((Date) map.get("ISSUE_TIME"));
			orderComplaintDetailData.setBillingServerTime((Date) map.get("BILLING_SERVER_TIME"));
			orderComplaintDetailData.setReadyToDispatch((Date) map.get("READY_TO_DISPATCH"));
			orderComplaintDetailData.setRiderAssignedAt((Date) map.get("RIDER_ASSIGNED_AT"));
			orderComplaintDetailData.setRiderArrivedAt((Date) map.get("RIDER_ARRIVED_AT"));
			orderComplaintDetailData.setOrderPickupTime((Date) map.get("ORDER_PICKUP_TIME"));
			orderComplaintDetailData.setOrderDeliveryTime((Date) map.get("ORDER_DELIVERY_TIME"));
			if(Objects.nonNull(map.get("PREPERATION_TIME")) && StringUtils.isBlank(map.get("PREPERATION_TIME").toString())) {
				orderComplaintDetailData.setPreperationTime(Long.parseLong(map.get("PREPERATION_TIME").toString()));
			}
			if(Objects.nonNull(map.get("RIDER_ARRIVED_TIME")) && StringUtils.isBlank(map.get("RIDER_ARRIVED_TIME").toString())) {
				orderComplaintDetailData.setRiderArrivedTime(Long.parseLong(map.get("RIDER_ARRIVED_TIME").toString()));
			}
			if(Objects.nonNull(map.get("RIDER_ASSIGNED_TIME")) && StringUtils.isBlank(map.get("RIDER_ASSIGNED_TIME").toString())) {
				orderComplaintDetailData.setRiderAssignedTime(Long.parseLong(map.get("RIDER_ASSIGNED_TIME").toString()));
			}
			if(Objects.nonNull(map.get("RIDER_PICKED_TIME")) && StringUtils.isBlank(map.get("RIDER_PICKED_TIME").toString())) {
				orderComplaintDetailData.setRiderPickedTime(Long.parseLong(Objects.toString(map.get("RIDER_PICKED_TIME"), "")));
			}
			if(Objects.nonNull(map.get("RIDER_DELIVERY_TIME")) && StringUtils.isBlank(map.get("RIDER_DELIVERY_TIME").toString())) {
				orderComplaintDetailData.setRiderDeliveryTime(Long.parseLong(map.get("RIDER_DELIVERY_TIME").toString()));
			}
			if(Objects.nonNull(map.get("TOTAL_DELIVERY_TIME")) && StringUtils.isBlank(map.get("TOTAL_DELIVERY_TIME").toString())) {
				orderComplaintDetailData.setTotalDeliveryTime(Long.parseLong(map.get("TOTAL_DELIVERY_TIME").toString()));
			}
			orderComplaintDetailData.setRiderLateArrived(Objects.toString(map.get("RIDER_LATE_ARRIVED"),""));
			orderComplaintDetailData.setOrderLateDelivered(Objects.toString(map.get("ORDER_LATE_DELIVERED"),""));
			orderComplaintDetailData.setFoodPreparedLate(Objects.toString(map.get("FOOD_PREPARED_LATE"),""));
			orderComplaintDetailData.setBrandName(Objects.toString(map.get("BRAND_NAME"),""));
			orderComplaintDetailData.setBranchId(Integer.parseInt(Objects.toString(map.get("BRANCH_ID"),"")));
			orderComplaintDetailData.setBusinessUid(Objects.toString(map.get("BUSINESS_UID"),""));
			orderComplaintDetailData.setCafeManager(Objects.toString(map.get("CAFE_MANAGER"),""));
			orderComplaintDetailData.setCafeManagerId(Objects.toString(map.get("CAFE_MANAGER_ID"),""));
			orderComplaintDetailData.setCafeManagerContact(Objects.toString(map.get("CAFE_MANAGER_CONTACT"), ""));
			orderComplaintDetailData.setOrderSourceId(Objects.toString(map.get("ORDER_SOURCE_ID"), ""));
			orderComplaintDetailData.setOrderId(Integer.parseInt(Objects.toString(map.get("ORDER_ID"), "")));
			orderComplaintDetailData.setGeneratedOrderId(Objects.toString(map.get("GENERATED_ORDER_ID"), ""));
			orderComplaintDetailData.setCustomerName(Objects.toString(map.get("CUSTOMER_NAME"), ""));
			orderComplaintDetailData.setPartnerCode(Objects.toString(map.get("PARTNER_CODE"), ""));
			orderComplaintDetailData.setItems(Objects.toString(map.get("ITEMS"), ""));
			orderComplaintDetailData.setItemNames(Objects.toString(map.get("ITEM_NAMES"), ""));
			orderComplaintDetailData.setRevenue((BigDecimal) map.get("REVENUE"));
			orderComplaintDetailData.setTotalAmount((BigDecimal) map.get("TOTAL_AMOUNT"));
			orderComplaintDetailData.setDiscount((BigDecimal) map.get("DISCOUNT"));
			orderComplaintDetailData.setRiderName(Objects.toString(map.get("RIDER_NAME"), ""));
			orderComplaintDetailData.setRiderContact(Objects.toString(map.get("RIDER_CONTACT"), ""));
			orderComplaintDetailData.setCustomerComplaint(Objects.toString(map.get("CUTSOMER_COMPLAINT"), ""));
			orderComplaintDetailData.setIssueType(Objects.toString(map.get("ISSUE_TYPE"), ""));
			orderComplaintDetailData.setComplaintItems(Objects.toString(map.get("COMPLAINT_ITEMS"), ""));
			orderComplaintDetailData.setComplaintTime(Objects.toString(map.get("COMPLAINT_TIME"), ""));
			orderComplaintDetailData.setPreviousItems(Objects.toString(map.get("PREVIOUS_ITEMS"), ""));
			orderComplaintDetailData.setModifiedItems(Objects.toString(map.get("MODIFIED_ITEMS"), ""));
			orderComplaintDetailData.setComplaintType(Objects.toString(map.get("COMPLAINT_TYPE"), ""));
			orderComplaintDetailDao.save(orderComplaintDetailData);
		}catch (Exception e){
			LOG.info("Error in save Customer complaint Data for order source Id : {} and Error is e : {}",
					map.get("ORDER_SOURCE_ID").toString(),e);
		}
	}

	@Override
	public List<com.stpl.tech.kettle.data.model.OrderItem> getComboOrderItems(Integer comboOrderItemId){
		return dao.getComboOrderItems(comboOrderItemId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<String> getOrdersForUnitFromStatus(Integer unitId, List<String> statusList) {
		return dao.getOrdersForUnitFromStatus(unitId, statusList);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Order> getCancelledOrdersByDateRange(Integer unitId, Date startDate, Date endDate) throws DataNotFoundException {
		return dao.getCancelledOrdersByDateRange(unitId, startDate, endDate);
	}

}

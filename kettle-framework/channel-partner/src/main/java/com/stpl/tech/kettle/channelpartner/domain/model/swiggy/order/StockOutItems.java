package com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockOutItems {

    @JsonProperty(value = "item_id")
    private String itemId;

    @JsonProperty(value = "next_available_time_epoch")
    private long nextAvailableTimeEpoch;
}
